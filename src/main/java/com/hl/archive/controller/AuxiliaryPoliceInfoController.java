package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.AuxiliaryPoliceInfoQueryDTO;
import com.hl.archive.domain.dto.PoliceStatisticsDTO;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.service.AuxiliaryPoliceInfoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/auxiliaryPoliceInfo")
@RequiredArgsConstructor
@Api(tags = "辅警信息")
public class AuxiliaryPoliceInfoController {

    private final AuxiliaryPoliceInfoService auxiliaryPoliceInfoService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<AuxiliaryPoliceInfo>> page(@RequestBody AuxiliaryPoliceInfoQueryDTO dto) {
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.pageList(dto);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody AuxiliaryPoliceInfo auxiliaryPoliceInfo) {
        auxiliaryPoliceInfo.setSourceType(1);
        String idCard = auxiliaryPoliceInfo.getIdCard();
        AuxiliaryPoliceInfo one = auxiliaryPoliceInfoService.lambdaQuery()
                .eq(AuxiliaryPoliceInfo::getIdCard, idCard)
                .last("limit 1")
                .one();
        if (one != null) {
            auxiliaryPoliceInfo.setId(one.getId());
            return R.ok(auxiliaryPoliceInfoService.updateById(auxiliaryPoliceInfo));
        }
        return R.ok(auxiliaryPoliceInfoService.save(auxiliaryPoliceInfo));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody AuxiliaryPoliceInfo auxiliaryPoliceInfo) {
        auxiliaryPoliceInfo.setChangeStatus(1);
        return R.ok(auxiliaryPoliceInfoService.updateById(auxiliaryPoliceInfo));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody AuxiliaryPoliceInfo auxiliaryPoliceInfo) {
        AuxiliaryPoliceInfo infoServiceById = auxiliaryPoliceInfoService.getById(auxiliaryPoliceInfo.getId());
        if (infoServiceById.getSourceType() == 0) {
            return R.fail("非手动添加数据,不能删除");
        }
        return R.ok(auxiliaryPoliceInfoService.removeById(auxiliaryPoliceInfo));
    }

    @PostMapping("/exportAuxiliaryPoliceInfo")
    @ApiOperation("导出辅警信息")
    public void exportAuxiliaryPoliceInfo(@RequestBody AuxiliaryPoliceInfoQueryDTO dto, HttpServletResponse response) throws IOException {
        auxiliaryPoliceInfoService.exportAuxiliaryPoliceInfo(dto, response);
    }



    @PostMapping("/statisticsPoliceByOrgId")
    @ApiOperation("按部门统计辅警人数、年龄段、学历、平均年龄")
    public R<PoliceStatisticsDTO> statisticsPoliceByOrgId(@RequestBody StatisticsQueryRequest request) {
        return R.ok(auxiliaryPoliceInfoService.getAuxiliaryPoliceStatisticsByOrgId(request));
    }

    @PostMapping("/statisticsDrillDown")
    @ApiOperation("辅警统计数字穿透查询 - 根据统计类型查询辅警基本信息列表")
    public R<List<AuxiliaryPoliceInfo>> statisticsDrillDown(@RequestBody StatisticsDrillDownRequest request) {
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.getAuxiliaryPoliceListByStatisticsType(request);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }
}
