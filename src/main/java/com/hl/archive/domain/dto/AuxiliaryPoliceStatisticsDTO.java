package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 辅警统计返回DTO
 */
@Data
@ApiModel(description = "辅警统计返回DTO")
public class AuxiliaryPoliceStatisticsDTO {

    /**
     * 部门代码
     */
    @ApiModelProperty(value = "部门代码")
    private String department;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 总人数
     */
    @ApiModelProperty(value = "总人数")
    private Integer totalCount = 0;

    /**
     * 年龄段统计
     */
    @ApiModelProperty(value = "20-30岁人数")
    private Integer age20To30 = 0;

    @ApiModelProperty(value = "30-40岁人数")
    private Integer age30To40 = 0;

    @ApiModelProperty(value = "40-50岁人数")
    private Integer age40To50 = 0;

    @ApiModelProperty(value = "50岁以上人数")
    private Integer age50Up = 0;

    /**
     * 学历统计 - 按照新的分类标准
     */
    @ApiModelProperty(value = "初中及以下学历人数")
    private Integer eduJuniorAndBelow = 0;

    @ApiModelProperty(value = "中专学历人数")
    private Integer eduTechnical = 0;

    @ApiModelProperty(value = "高中学历人数")
    private Integer eduHighSchool = 0;

    @ApiModelProperty(value = "大专学历人数")
    private Integer eduCollege = 0;

    @ApiModelProperty(value = "本科及以上学历人数")
    private Integer eduBachelorAndAbove = 0;

    /**
     * 平均年龄
     */
    @ApiModelProperty(value = "平均年龄")
    private Double avgAge = 0.0;

    /**
     * 性别统计
     */
    @ApiModelProperty(value = "男性人数")
    private Integer maleCount = 0;

    @ApiModelProperty(value = "女性人数")
    private Integer femaleCount = 0;

    /**
     * 在岗状态统计
     */
    @ApiModelProperty(value = "在岗人数")
    private Integer onDutyCount = 0;

    @ApiModelProperty(value = "不在岗人数")
    private Integer offDutyCount = 0;

    /**
     * 来源统计（如果辅警也有类似分类的话）
     */
    @ApiModelProperty(value = "转业军人")
    private Integer zyjrCount = 0;

    @ApiModelProperty(value = "警校毕业")
    private Integer jxbyCount = 0;

    @ApiModelProperty(value = "社会招录")
    private Integer shzlCount = 0;

    @ApiModelProperty(value = "其他转入")
    private Integer qtzrCount = 0;

    @ApiModelProperty(value = "公安系统调入")
    private Integer gaxtdrCount = 0;
}
