package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyzwzj;
import com.hl.orasync.domain.VWjRyzwzjToPolicePositionRankMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__463;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__463.class,
    uses = {ConversionUtils.class,VWjRyzwzjToPolicePositionRankMapper.class},
    imports = {}
)
public interface PolicePositionRankToVWjRyzwzjMapper extends BaseMapper<PolicePositionRank, VWjRyzwzj> {
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zwmc",
      source = "positionName"
  )
  @Mapping(
      target = "xzjsj",
      source = "currentRankDate"
  )
  @Mapping(
      target = "zwsxsj",
      source = "currentPositionDate"
  )
  @Mapping(
      target = "rzwh",
      source = "appointmentDocument"
  )
  @Mapping(
      target = "gazwjb",
      source = "policePositionLevel"
  )
  VWjRyzwzj convert(PolicePositionRank source);

  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zwmc",
      source = "positionName"
  )
  @Mapping(
      target = "xzjsj",
      source = "currentRankDate"
  )
  @Mapping(
      target = "zwsxsj",
      source = "currentPositionDate"
  )
  @Mapping(
      target = "rzwh",
      source = "appointmentDocument"
  )
  @Mapping(
      target = "gazwjb",
      source = "policePositionLevel"
  )
  VWjRyzwzj convert(PolicePositionRank source, @MappingTarget VWjRyzwzj target);
}
