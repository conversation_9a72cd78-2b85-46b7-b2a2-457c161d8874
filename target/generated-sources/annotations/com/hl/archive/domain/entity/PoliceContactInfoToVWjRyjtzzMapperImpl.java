package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjtzz;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-05T09:05:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceContactInfoToVWjRyjtzzMapperImpl implements PoliceContactInfoToVWjRyjtzzMapper {

    @Override
    public VWjRyjtzz convert(PoliceContactInfo source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjtzz vWjRyjtzz = new VWjRyjtzz();

        vWjRyjtzz.setJtdz( source.getHomeAddress() );
        vWjRyjtzz.setGmsfhm( source.getIdCard() );
        vWjRyjtzz.setShhmdh( source.getMobileShortNumber() );
        vWjRyjtzz.setJtdh( source.getHomePhone() );
        vWjRyjtzz.setShhm( source.getMobilePhone() );

        return vWjRyjtzz;
    }

    @Override
    public VWjRyjtzz convert(PoliceContactInfo source, VWjRyjtzz target) {
        if ( source == null ) {
            return target;
        }

        target.setJtdz( source.getHomeAddress() );
        target.setGmsfhm( source.getIdCard() );
        target.setShhmdh( source.getMobileShortNumber() );
        target.setJtdh( source.getHomePhone() );
        target.setShhm( source.getMobilePhone() );

        return target;
    }
}
